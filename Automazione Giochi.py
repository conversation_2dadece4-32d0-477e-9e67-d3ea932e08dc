import os
import time
import tkinter as tk
from tkinter import messagebox, ttk
import requests
import subprocess
import sys
from selenium.webdriver.edge.service import Service
from selenium.webdriver.edge.options import Options
from webdriver_manager.microsoft import EdgeChromiumDriverManager
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import Web<PERSON>river<PERSON>ait
from selenium.webdriver.support import expected_conditions as EC

# Configurazioni
CARTELLA_GIOCHI = os.path.join("E:", "cartella vacanze", "giochi")
# Path corretti per la cartella liste
BASE_DIR = os.path.dirname(os.path.abspath(__file__))
LISTE_DIR = os.path.join(BASE_DIR, "liste")
FILE_LISTA_GIOCHI = os.path.join(LISTE_DIR, "lista_giochi.txt")
FILE_CARTELLE_GIOCHI = os.path.join(LISTE_DIR, "cartelle_giochi.txt")
FILE_LINK_DOWNLOAD = os.path.join(LISTE_DIR, "lista link download.txt")
API_KEY = "e2a1974678b37386fef69bb3638a1fb36263b78a8be244c04795ada0fa250d3d"
TELEGRAM_BOT_SCRIPT = os.path.join(BASE_DIR, "Telegram Bot", "TelegramBot.py")

class GameSearchApp:
    def __init__(self, root):
        self.root = root
        self.root.title("Game Finder")
        self.root.geometry("500x280")
        self.root.resizable(False, False)
        
        self.start_time = None
        self.progress = tk.DoubleVar()
        self.status = tk.StringVar(value="Inizializzazione...")
        self.tempo = tk.StringVar()
        
        self.setup_ui()
        self.root.after(100, self.avvia_procedura)
        
    def setup_ui(self):
        style = ttk.Style()
        style.theme_use("clam")
        style.configure("TProgressbar", thickness=22, troughcolor="#eceff1", 
                       background="#4CAF50", troughrelief="flat")
        
        main_frame = ttk.Frame(self.root, padding=15)
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # Titolo principale
        ttk.Label(main_frame, 
                 text="Ricerca Giochi Automatizzata",
                 font=("Segoe UI", 14, "bold"),
                 foreground="#263238").pack(pady=3)
        
        # Stato corrente
        ttk.Label(main_frame, 
                 textvariable=self.status,
                 font=("Segoe UI", 10),
                 foreground="#37474f").pack(pady=5)
        
        # Barra di progresso con percentuale
        progress_frame = ttk.Frame(main_frame)
        progress_frame.pack(fill=tk.X, pady=10)
        
        ttk.Progressbar(progress_frame, 
                       variable=self.progress,
                       maximum=100,
                       style="TProgressbar").pack(side=tk.LEFT, fill=tk.X, expand=True)
        
        ttk.Label(progress_frame, 
                 textvariable=self.progress,
                 font=("Segoe UI", 10),
                 width=5).pack(side=tk.RIGHT, padx=5)
        
        # Informazioni tempo
        ttk.Label(main_frame,
                 textvariable=self.tempo,
                 font=("Segoe UI", 8),
                 foreground="#78909c").pack(pady=5)

    def aggiorna_interfaccia(self, valore, messaggio):
        now = time.time()
        elapsed = now - self.start_time
        percent = max(1, valore)
        remaining = (elapsed / percent) * (100 - percent) if percent > 0 else 0
        
        self.progress.set(valore)
        self.status.set(messaggio)
        self.tempo.set(
            f"⏱️ Trascorso: {time.strftime('%M:%S', time.gmtime(elapsed))} | "
            f"⏳ Stimato: {time.strftime('%M:%S', time.gmtime(remaining))} | "
            f"📊 Progresso: {valore:.1f}%"
        )
        self.root.update_idletasks()
        time.sleep(0.05)  # Per fluidità animazione

    def apri_vpn(self):
        try:
            self.aggiorna_interfaccia(5, "🔒 Connessione a ProtonVPN...")
            os.system(r'start "" "c:\Program Files\Proton\VPN\ProtonVPN.Launcher.exe"')
            time.sleep(6)
            return True
        except Exception as e:
            messagebox.showerror("Errore", f"Connessione VPN fallita: {str(e)}")
            return False

    def genera_cartelle(self):
        try:
            self.aggiorna_interfaccia(10, "📂 Scansione cartelle giochi...")
            if not os.path.exists(CARTELLA_GIOCHI):
                raise Exception("Cartella giochi non trovata!")
            
            cartelle = [c for c in os.listdir(CARTELLA_GIOCHI) 
                       if os.path.isdir(os.path.join(CARTELLA_GIOCHI, c))]
            
            with open(FILE_CARTELLE_GIOCHI, "w", encoding="utf-8") as f:
                f.write("\n".join(cartelle))
            
            return True
        except Exception as e:
            messagebox.showerror("Errore", f"Scansione fallita: {str(e)}")
            return False

    def cerca_sito(self, gioco, nome, url, selector):
        self.aggiorna_interfaccia(
            self.progress.get() + 2, 
            f"🌐 Ricerca su {nome} per '{gioco}'..."
        )
        
        options = Options()
        options.add_argument("--headless")
        service = Service(EdgeChromiumDriverManager().install())
        
        try:
            driver = webdriver.Edge(service=service, options=options)
            driver.implicitly_wait(8)
            driver.get(url)
            
            try:
                elementi = WebDriverWait(driver, 10).until(
                    EC.presence_of_all_elements_located((By.CSS_SELECTOR, selector))
                )
                
                for elemento in elementi:
                    if gioco.lower() in elemento.text.lower():
                        link = elemento.find_element(By.TAG_NAME, "a").get_attribute("href")
                        with open(FILE_LINK_DOWNLOAD, "a", encoding="utf-8") as f:
                            f.write(f"{link}\n")
                        driver.quit()
                        return True
            except Exception:
                pass
            
            driver.quit()
            return False
            
        except Exception:
            return False

    def cerca_portali(self, gioco):
        siti = [
            ("Online-Fix", f"https://online-fix.me/?s={gioco}", ".post-title"),
            ("FitGirl", f"https://fitgirl-repacks.site/?s={gioco}", ".entry-title"),
            ("SteamRIP", f"https://steamrip.com/?s={gioco}", ".post-title")
        ]
        
        for sito in siti:
            if self.cerca_sito(gioco, *sito):
                return True
        return False

    def cerca_rezione(self, gioco):
        self.aggiorna_interfaccia(
            self.progress.get() + 5, 
            f"🔍 Scansione Rezi.one per '{gioco}'..."
        )
        
        url = "https://search.rezi.one/indexes/rezi/search"
        headers = {"Authorization": f"Bearer {API_KEY}", "Content-Type": "application/json"}
        payload = {"q": gioco, "limit": 5}
        
        try:
            response = requests.post(url, json=payload, headers=headers)
            if response.status_code == 200:
                for hit in response.json().get("hits", []):
                    link = hit.get("link", "")
                    if "gamesdrive" not in link:
                        with open(FILE_LINK_DOWNLOAD, "a", encoding="utf-8") as f:
                            f.write(f"{link}\n")
                        return True
            return False
        except Exception:
            return False

    def gestisci_gioco(self, gioco, azione):
        try:
            with open(FILE_LISTA_GIOCHI, "r+", encoding="utf-8") as f:
                lines = f.readlines()
                f.seek(0)
                for line in lines:
                    if line.strip().lower() == gioco.lower():
                        if azione == "rimuovi":
                            continue
                        elif azione == "sbarra":
                            testo = ''.join([f'{c}\u0336' for c in line.strip()])
                            f.write(f"{testo}\n")
                    else:
                        f.write(line)
                f.truncate()
            return True
        except Exception:
            return False

    def avvia_telegram_bot(self):
        """Avvia il TelegramBot per il monitoraggio automatico"""
        try:
            print("🚀 Avvio TelegramBot per monitoraggio...")
            # Avvia il bot in background
            subprocess.Popen([sys.executable, TELEGRAM_BOT_SCRIPT],
                           creationflags=subprocess.CREATE_NEW_CONSOLE)
            print("✅ TelegramBot avviato con successo!")
            return True
        except Exception as e:
            print(f"❌ Errore nell'avviare TelegramBot: {e}")
            return False

    def avvia_procedura(self):
        self.start_time = time.time()
        
        try:
            if not self.apri_vpn():
                return
                
            if not self.genera_cartelle():
                return
                
            with open(FILE_CARTELLE_GIOCHI, "r", encoding="utf-8") as f:
                cartelle = [line.strip().lower() for line in f if line.strip()]
            
            with open(FILE_LISTA_GIOCHI, "r", encoding="utf-8") as f:
                giochi = [line.strip().lower() for line in f 
                         if line.strip() and '\u0336' not in line]
            
            # Fase 1: Pulizia giochi esistenti
            step_pulizia = 15 / len(giochi) if giochi else 0
            for gioco in giochi.copy():
                if gioco in cartelle:
                    self.aggiorna_interfaccia(
                        self.progress.get() + step_pulizia,
                        f"🧹 Rimozione '{gioco}'..."
                    )
                    self.gestisci_gioco(gioco, "rimuovi")
                    giochi.remove(gioco)
            
            # Fase 2: Ricerca attiva
            step_ricerca = 70 / len(giochi) if giochi else 0
            for gioco in giochi:
                self.aggiorna_interfaccia(
                    self.progress.get() + step_ricerca,
                    f"🚀 Ricerca attiva: '{gioco}'"
                )
                
                if self.cerca_portali(gioco) or self.cerca_rezione(gioco):
                    self.gestisci_gioco(gioco, "sbarra")
                    self.aggiorna_interfaccia(100, "✅ Download trovato!")

                    # Avvia automaticamente il TelegramBot per il monitoraggio
                    if self.avvia_telegram_bot():
                        messagebox.showinfo("Successo",
                                          f"✅ Link per '{gioco}' salvato!\n🤖 TelegramBot avviato per il monitoraggio automatico!")
                    else:
                        messagebox.showinfo("Successo",
                                          f"✅ Link per '{gioco}' salvato!\n⚠️ Avvia manualmente TelegramBot per il monitoraggio")

                    self.root.destroy()
                    return
            
            self.aggiorna_interfaccia(100, "🏁 Ricerca completata")
            messagebox.showinfo("Info", "Nessun nuovo gioco trovato")
            self.root.destroy()
            
        except Exception as e:
            messagebox.showerror("Errore", str(e))
            self.root.destroy()

if __name__ == "__main__":
    root = tk.Tk()
    app = GameSearchApp(root)
    root.mainloop()