# Script PowerShell per mantenere il bot sempre attivo
param(
    [switch]$Install,
    [switch]$Uninstall,
    [switch]$Start,
    [switch]$Stop
)

$serviceName = "TelegramBotService"
$serviceDisplayName = "Telegram Bot Service"
$botPath = "c:\Users\<USER>\Desktop\Automazizzazione python\Telegram Bot\TelegramBot.py"
$pythonPath = (Get-Command python).Source

function Install-BotService {
    Write-Host "🔧 Installazione del servizio..." -ForegroundColor Yellow
    
    # Crea il wrapper script
    $wrapperScript = @"
import subprocess
import sys
import time
import logging

logging.basicConfig(filename='c:/temp/telegram_bot_service.log', level=logging.INFO)

while True:
    try:
        logging.info("Avvio bot...")
        result = subprocess.run([sys.executable, '$botPath'], capture_output=True, text=True)
        logging.error(f"Bot terminato con codice: {result.returncode}")
        logging.error(f"Stderr: {result.stderr}")
        time.sleep(10)  # Aspetta 10 secondi prima di riavviare
    except Exception as e:
        logging.error(f"Errore: {e}")
        time.sleep(30)
"@
    
    $wrapperPath = "c:\temp\telegram_bot_wrapper.py"
    $wrapperScript | Out-File -FilePath $wrapperPath -Encoding UTF8
    
    # Installa il servizio usando NSSM (Non-Sucking Service Manager)
    Write-Host "📦 Scaricamento NSSM..." -ForegroundColor Blue
    $nssmUrl = "https://nssm.cc/release/nssm-2.24.zip"
    $nssmZip = "c:\temp\nssm.zip"
    $nssmDir = "c:\temp\nssm"
    
    Invoke-WebRequest -Uri $nssmUrl -OutFile $nssmZip
    Expand-Archive -Path $nssmZip -DestinationPath $nssmDir -Force
    
    $nssmExe = Get-ChildItem -Path $nssmDir -Name "nssm.exe" -Recurse | Select-Object -First 1
    $nssmPath = Join-Path $nssmDir $nssmExe.DirectoryName $nssmExe.Name
    
    # Installa il servizio
    & $nssmPath install $serviceName $pythonPath $wrapperPath
    & $nssmPath set $serviceName DisplayName $serviceDisplayName
    & $nssmPath set $serviceName Description "Servizio per mantenere attivo il bot Telegram"
    & $nssmPath set $serviceName Start SERVICE_AUTO_START
    
    Write-Host "✅ Servizio installato!" -ForegroundColor Green
}

function Start-BotService {
    Write-Host "🚀 Avvio del servizio..." -ForegroundColor Green
    Start-Service -Name $serviceName
    Write-Host "✅ Servizio avviato!" -ForegroundColor Green
}

function Stop-BotService {
    Write-Host "🛑 Arresto del servizio..." -ForegroundColor Red
    Stop-Service -Name $serviceName
    Write-Host "✅ Servizio arrestato!" -ForegroundColor Green
}

function Uninstall-BotService {
    Write-Host "🗑️ Rimozione del servizio..." -ForegroundColor Red
    Stop-Service -Name $serviceName -ErrorAction SilentlyContinue
    sc.exe delete $serviceName
    Write-Host "✅ Servizio rimosso!" -ForegroundColor Green
}

# Menu principale
if ($Install) { Install-BotService }
elseif ($Uninstall) { Uninstall-BotService }
elseif ($Start) { Start-BotService }
elseif ($Stop) { Stop-BotService }
else {
    Write-Host "=== GESTIONE SERVIZIO TELEGRAM BOT ===" -ForegroundColor Cyan
    Write-Host "Uso: .\run_bot_service.ps1 [-Install|-Uninstall|-Start|-Stop]" -ForegroundColor Yellow
    Write-Host ""
    Write-Host "Opzioni:" -ForegroundColor White
    Write-Host "  -Install    Installa il servizio" -ForegroundColor Green
    Write-Host "  -Start      Avvia il servizio" -ForegroundColor Green
    Write-Host "  -Stop       Ferma il servizio" -ForegroundColor Red
    Write-Host "  -Uninstall  Rimuove il servizio" -ForegroundColor Red
}
