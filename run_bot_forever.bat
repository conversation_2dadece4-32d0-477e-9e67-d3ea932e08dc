@echo off
title Telegram Bot - Always Running
echo ========================================
echo    TELEGRAM BOT - MODALITA PERMANENTE
echo ========================================
echo.

:loop
echo [%date% %time%] Avvio del bot...
cd /d "c:\Users\<USER>\Desktop\Automazizzazione python"
python "Telegram Bot\TelegramBot.py"

echo.
echo [%date% %time%] Bot terminato. Riavvio in 5 secondi...
timeout /t 5 /nobreak >nul
goto loop
