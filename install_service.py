import win32serviceutil
import win32service
import win32event
import servicemanager
import socket
import sys
import os
import subprocess
import time

class TelegramBotService(win32serviceutil.ServiceFramework):
    _svc_name_ = "TelegramBotService"
    _svc_display_name_ = "Telegram Bot Service"
    _svc_description_ = "Servizio per mantenere attivo il bot Telegram"

    def __init__(self, args):
        win32serviceutil.ServiceFramework.__init__(self, args)
        self.hWaitStop = win32event.CreateEvent(None, 0, 0, None)
        socket.setdefaulttimeout(60)
        self.is_alive = True

    def SvcStop(self):
        self.ReportServiceStatus(win32service.SERVICE_STOP_PENDING)
        win32event.SetEvent(self.hWaitStop)
        self.is_alive = False

    def SvcDoRun(self):
        servicemanager.LogMsg(servicemanager.EVENTLOG_INFORMATION_TYPE,
                              servicemanager.PYS_SERVICE_STARTED,
                              (self._svc_name_, ''))
        self.main()

    def main(self):
        # Percorso al tuo script del bot
        bot_script = r"c:\Users\<USER>\Desktop\Automazizzazione python\Telegram Bot\TelegramBot.py"
        python_exe = sys.executable
        
        while self.is_alive:
            try:
                # Avvia il bot
                process = subprocess.Popen([python_exe, bot_script], 
                                         stdout=subprocess.PIPE, 
                                         stderr=subprocess.PIPE)
                
                # Aspetta che il processo finisca o che il servizio venga fermato
                while self.is_alive and process.poll() is None:
                    time.sleep(1)
                
                if process.poll() is not None and self.is_alive:
                    # Il bot si è chiuso inaspettatamente, riavvialo
                    servicemanager.LogErrorMsg(f"Bot crashed, restarting...")
                    time.sleep(5)  # Aspetta 5 secondi prima di riavviare
                    
            except Exception as e:
                servicemanager.LogErrorMsg(f"Errore nel servizio: {str(e)}")
                time.sleep(10)

if __name__ == '__main__':
    if len(sys.argv) == 1:
        servicemanager.Initialize()
        servicemanager.PrepareToHostSingle(TelegramBotService)
        servicemanager.StartServiceCtrlDispatcher()
    else:
        win32serviceutil.HandleCommandLine(TelegramBotService)
