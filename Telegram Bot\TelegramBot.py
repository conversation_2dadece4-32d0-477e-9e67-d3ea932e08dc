import logging
import sys
import asyncio
import os
import re
import requests
from urllib.parse import urlparse
from bs4 import BeautifulSoup
from telegram import Update, Bot
from telegram.ext import Application, CommandHandler, MessageHandler, filters, CallbackContext
import warnings
from bs4 import XMLParsedAsHTMLWarning
from selenium import webdriver
from selenium.webdriver.edge.options import Options
from selenium.common.exceptions import WebDriverException
from selenium.webdriver.common.by import By

warnings.filterwarnings("ignore", category=XMLParsedAsHTMLWarning)

if sys.platform == 'win32':
    from asyncio import WindowsSelectorEventLoopPolicy
    asyncio.set_event_loop_policy(WindowsSelectorEventLoopPolicy())

TOKEN = "7793262442:AAGcNzW5_NfAy7GvfQD86ayWjh1pTAtJVag"
OWNER_ID = 916408363
# Path corretti per la cartella liste
BASE_DIR = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))  # Torna alla directory principale
LISTE_DIR = os.path.join(BASE_DIR, "liste")
FILE_TXT = os.path.join(LISTE_DIR, "lista link download.txt")
DOWNLOAD_DIR = "processed"
REQUEST_HEADERS = {
    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
    'Accept-Language': 'en-US,en;q=0.9',
}
CHECK_INTERVAL = 10
FILE_TIMEOUT = 300

os.makedirs(DOWNLOAD_DIR, exist_ok=True)
logging.basicConfig(
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    level=logging.INFO,
    handlers=[logging.StreamHandler()]
)

def is_special_site(url):
    # Lista dei domini speciali
    special_domains = ["megadb.net", "paste.fitgirl-repacks.site", "altrodominio.esempio"]
    result = any(domain in url for domain in special_domains)
    print(f"[is_special_site] Controllo URL: {url} -> {'Speciale' if result else 'Normale'}")
    return result

def init_selenium_driver():
    print("🛠️ Inizializzazione driver Selenium...")
    edge_options = Options()
    edge_options.use_chromium = True
    edge_options.add_argument("--headless=new")
    edge_options.add_argument("--disable-gpu")
    edge_options.add_argument("--no-sandbox")
    edge_options.add_argument("--disable-dev-shm-usage")
    edge_options.add_argument("--log-level=3")
    
    try:
        driver = webdriver.Edge(options=edge_options)
        print("✅ Driver Selenium inizializzato")
        return driver
    except WebDriverException as e:
        print(f"❌ Errore Selenium Edge: {e}")
        logging.error(f"Errore Selenium Edge: {e}")
        return None

class TorrentEngine:
    def __init__(self):
        print("⚡ Inizializzazione TorrentEngine...")
        self.active = False
        self.current_link = None
        self.bot = Bot(token=TOKEN)
        self.driver = init_selenium_driver()
        self.file_event = asyncio.Event()
        self.lock = asyncio.Lock()
        self.queue = asyncio.Queue()
        print("✅ TorrentEngine pronto")

    def log_site_links(self, base_url, links):
        # Crea (o apre) un file di log sul Desktop
        desktop = os.path.join(os.path.expanduser("~"), "Desktop")
        log_file = os.path.join(desktop, "special_sites_log.txt")
        print(f"[log_site_links] Salvataggio log nel file: {log_file}")
        with open(log_file, "a", encoding="utf-8") as f:
            f.write(f"--- Nuova visita a: {base_url} ---\n")
            for link in links:
                f.write(link + "\n")
            f.write("------\n")
        print("[log_site_links] Salvataggio completato")

    async def process_queue(self):
        print("🔄 Avvio del monitoraggio della coda...")
        while True:
            try:
                print("\n📥 Caricamento link dal file di input...")
                links = await self.load_links()
                print(f"📚 {len(links)} link trovati nel file di input")

                for link in links:
                    if not await self.is_processed(link):
                        print(f"➕ Aggiungo alla coda: {link}")
                        await self.queue.put(link)
                    else:
                        print(f"🔎 Link già processato: {link}")
                
                while not self.queue.empty():
                    url = await self.queue.get()
                    print(f"\n🔍 Elaborazione URL: {url}")
                    async with self.lock:
                        await self.handle_link(url)
                    self.queue.task_done()
                    print(f"⏳ Attesa {CHECK_INTERVAL} secondi per il prossimo link...")
                    await asyncio.sleep(CHECK_INTERVAL)
                
                print(f"🕒 Nessun nuovo link. Riprovo tra {CHECK_INTERVAL} secondi...")
                await asyncio.sleep(CHECK_INTERVAL)
            except Exception as e:
                print(f"❌ Errore in process_queue: {str(e)}")
                logging.error(f"Errore process_queue: {str(e)}")

    async def load_links(self):
        try:
            print(f"📖 Lettura del file {FILE_TXT}...")
            with open(FILE_TXT, "r", encoding="utf-8") as f:
                links = [lnk.strip() for lnk in f.readlines() if lnk.strip()]
                print(f"🔗 Trovati {len(links)} link nel file di input")
                return links
        except Exception as e:
            print(f"❌ Errore nella lettura del file: {str(e)}")
            logging.error(f"Errore lettura file: {str(e)}")
            return []

    async def is_processed(self, url):
        print(f"🔎 Verifica se {url} è già stato processato...")
        processed_file = os.path.join(DOWNLOAD_DIR, "processed.txt")
        try:
            with open(processed_file, "r", encoding="utf-8") as f:
                found = url in f.read()
                print(f"✔️ Stato processato: {found}")
                return found
        except FileNotFoundError:
            print("⚠️ File processed.txt non trovato; considerato non processato")
            return False

    async def mark_processed(self, url):
        print(f"📝 Marcatura di {url} come processato...")
        processed_file = os.path.join(DOWNLOAD_DIR, "processed.txt")
        with open(processed_file, "a", encoding="utf-8") as f:
            f.write(f"{url}\n")
        print("✅ Marcato con successo")

    async def handle_link(self, url):
        self.current_link = url
        print(f"\n🚀 Inizio elaborazione per: {url}")
        try:
            # Trova ed estrai i link senza cliccare
            links_found = await self.find_links(url)
            if links_found:
                # Registra i link nel file di log sul Desktop
                self.log_site_links(url, links_found)
                # Filtra i link speciali
                special_links = [l for l in links_found if is_special_site(l)]
                if special_links:
                    msg = f"🚨 Speciale trovato su {url}:\n" + "\n".join(special_links)
                    print("[handle_link] Trovato almeno un link speciale. Invio messaggio Telegram.")
                    # Invia un unico messaggio con i link speciali
                    await self.bot.send_message(chat_id=OWNER_ID, text=msg)
                    print(f"🗑️ Rimozione di {url} dal file di input...")
                    await self.remove_from_file(url)
                else:
                    print("[handle_link] Nessun link speciale trovato; nessun messaggio inviato a Telegram.")
            else:
                print("❌ Nessun link raccolto per questo URL")
        except Exception as e:
            print(f"❌ Errore in handle_link: {str(e)}")
            logging.error(f"Errore handle_link: {str(e)}")
        finally:
            print("🔚 Fine elaborazione per questo URL")
            self.current_link = None

    async def find_links(self, url):
        print(f"\n🕵️  Analisi di {url}")
        # Per siti speciali, ritorna direttamente l'URL
        if is_special_site(url):
            print("🔍 Sito speciale rilevato: uso l'URL originale")
            return [url]

        links_collected = []
        try:
            if self.driver:
                print("🌐 Uso Selenium per estrarre i link (senza clic)...")
                print(f"🔗 Navigazione verso {url}")
                self.driver.get(url)
                await asyncio.sleep(5)
                current_url = self.driver.current_url
                print(f"🌐 URL corrente: {current_url}")
                # XPath robusto: cerca <a> e <button> per classi o testo che contengano 'download' o 'torrent'
                xpath_expr = (
                    "//a["
                    "contains(translate(@class, 'ABCDEFGHIJKLMNOPQRSTUVWXYZ', 'abcdefghijklmnopqrstuvwxyz'), 'download') or "
                    "contains(translate(@class, 'ABCDEFGHIJKLMNOPQRSTUVWXYZ', 'abcdefghijklmnopqrstuvwxyz'), 'torrent') or "
                    "contains(translate(text(), 'ABCDEFGHIJKLMNOPQRSTUVWXYZ', 'abcdefghijklmnopqrstuvwxyz'), 'download') or "
                    "contains(translate(text(), 'ABCDEFGHIJKLMNOPQRSTUVWXYZ', 'abcdefghijklmnopqrstuvwxyz'), 'torrent')"
                    "] | "
                    "//button["
                    "contains(translate(@class, 'ABCDEFGHIJKLMNOPQRSTUVWXYZ', 'abcdefghijklmnopqrstuvwxyz'), 'download') or "
                    "contains(translate(@class, 'ABCDEFGHIJKLMNOPQRSTUVWXYZ', 'abcdefghijklmnopqrstuvwxyz'), 'torrent') or "
                    "contains(translate(text(), 'ABCDEFGHIJKLMNOPQRSTUVWXYZ', 'abcdefghijklmnopqrstuvwxyz'), 'download') or "
                    "contains(translate(text(), 'ABCDEFGHIJKLMNOPQRSTUVWXYZ', 'abcdefghijklmnopqrstuvwxyz'), 'torrent')"
                    "]"
                )
                print(f"[find_links] Uso XPath: {xpath_expr}")
                elements = self.driver.find_elements(By.XPATH, xpath_expr)
                print(f"🔘 Trovati {len(elements)} elementi")
                for i, elem in enumerate(elements):
                    print(f"🖱️ Elaborazione elemento {i+1}")
                    try:
                        href = elem.get_attribute("href")
                        if href:
                            print(f"Elemento {i+1} -> href: {href}")
                            links_collected.append(href)
                        else:
                            text = elem.text.strip()
                            print(f"Elemento {i+1} senza href, testo: {text}")
                            links_collected.append(text if text else "Elemento senza testo")
                    except Exception as e:
                        print(f"❌ Errore nell'elaborazione dell'elemento {i+1}: {e}")
            else:
                print("🌐 Selenium non disponibile; uso requests e BeautifulSoup...")
                page_source = requests.get(url, headers=REQUEST_HEADERS).text
                soup = BeautifulSoup(page_source, 'lxml')
                elements = soup.select("a, button")
                print(f"🔘 Trovati {len(elements)} elementi con BeautifulSoup")
                for i, elem in enumerate(elements):
                    href = elem.get("href")
                    if href:
                        print(f"Elemento {i+1} -> href: {href}")
                        links_collected.append(href)
                    else:
                        text = elem.get_text().strip()
                        print(f"Elemento {i+1} senza href, testo: {text}")
                        links_collected.append(text if text else "Elemento senza testo")
            
            if links_collected:
                print("[find_links] Link raccolti:")
                for link in links_collected:
                    print("  -", link)
                return links_collected
            else:
                print("❌ Nessun link trovato durante l'analisi")
                return None
        except Exception as e:
            print(f"❌ Errore in find_links: {str(e)}")
            return None

    async def remove_from_file(self, url):
        print(f"🗑️ Rimozione del link {url} dal file {FILE_TXT}...")
        try:
            with open(FILE_TXT, "r", encoding="utf-8") as f:
                lines = f.readlines()
            with open(FILE_TXT, "w", encoding="utf-8") as f:
                for l in lines:
                    if l.strip() != url:
                        f.write(l)
                    else:
                        print(f"🔍 Rimosso: {l.strip()}")
            print("✅ Link rimosso con successo")
        except Exception as e:
            print(f"❌ Errore durante la rimozione del link: {str(e)}")
            logging.error(f"Errore rimozione link: {str(e)}")

async def handle_file(update: Update, context: CallbackContext):
    print("\n📤 File ricevuto!")
    if update.effective_user.id != OWNER_ID:
        print("❌ Utente non autorizzato a inviare file")
        return
    
    try:
        file = await update.message.document.get_file()
        filename = os.path.join(DOWNLOAD_DIR, update.message.document.file_name)
        print(f"💾 Download del file in: {filename}")
        await file.download_to_drive(filename)
        engine.file_event.set()
        print("✅ File salvato e evento attivato")
        await update.message.reply_text("✅ File ricevuto! Procedo con il prossimo link.")
    except Exception as e:
        print(f"❌ Errore nella gestione del file: {str(e)}")
        logging.error(f"Errore handle_file: {str(e)}")

async def start(update: Update, context: CallbackContext):
    print("\n🟢 Comando /start ricevuto")
    if update.effective_user.id == OWNER_ID:
        await update.message.reply_text("⚡ Bot attivo! Monitoraggio iniziato.")
        print("✅ Messaggio di avvio inviato")

async def main():
    print("🚀 Avvio applicazione...")
    global engine
    engine = TorrentEngine()
    app = Application.builder().token(TOKEN).build()
    
    app.add_handler(CommandHandler("start", start))
    app.add_handler(MessageHandler(filters.Document.ALL, handle_file))
    
    await app.initialize()
    await app.start()
    print("🔄 Avvio del processo di coda...")
    asyncio.create_task(engine.process_queue())
    
    try:
        print("🔎 Inizio polling...")
        await app.updater.start_polling()
        while True:
            await asyncio.sleep(3600)
    except (asyncio.CancelledError, KeyboardInterrupt):
        print("\n🛑 Arresto in corso...")
        await app.stop()
        if engine.driver:
            engine.driver.quit()
            print("🔚 Driver Selenium chiuso")

def run_with_restart():
    """Esegue il bot con auto-restart in caso di errori"""
    max_retries = 5
    retry_count = 0

    while retry_count < max_retries:
        try:
            print(f"⏳ Avvio script... (Tentativo {retry_count + 1}/{max_retries})")
            asyncio.run(main())
            break  # Se arriviamo qui, il bot è terminato normalmente
        except KeyboardInterrupt:
            print("\n🛑 Arresto manuale richiesto")
            break
        except Exception as e:
            retry_count += 1
            print(f"❌ Errore critico: {str(e)}")
            logging.error(f"Errore critico: {str(e)}")

            if retry_count < max_retries:
                wait_time = min(60 * retry_count, 300)  # Max 5 minuti
                print(f"🔄 Riavvio in {wait_time} secondi...")
                import time
                time.sleep(wait_time)
            else:
                print("💀 Troppi errori consecutivi. Arresto definitivo.")
                break

if __name__ == "__main__":
    run_with_restart()
