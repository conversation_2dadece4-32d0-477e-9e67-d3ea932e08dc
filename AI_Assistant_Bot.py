import logging
import asyncio
import os
import sys
import subprocess
import json
import requests
from telegram import Update
from telegram.ext import Application, CommandHandler, MessageHandler, filters, CallbackContext

# Configurazione
TOKEN = "7793262442:AAGcNzW5_NfAy7GvfQD86ayWjh1pTAtJVag"  # Stesso token del bot esistente
OWNER_ID = 916408363
OLLAMA_URL = "http://localhost:11434/api/generate"
DEFAULT_MODEL = "llama3.2"  # Cambia con il tuo modello preferito

# File paths
LISTA_GIOCHI_FILE = r"c:\Users\<USER>\Desktop\lista_giochi.txt"
AUTOMAZIONE_SCRIPT = r"c:\Users\<USER>\Desktop\Automazizzazione python\Automazione Giochi.py"
TELEGRAM_BOT_SCRIPT = r"c:\Users\<USER>\Desktop\Automazizzazione python\Telegram Bot\TelegramBot.py"

# Setup logging
logging.basicConfig(
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    level=logging.INFO
)
logger = logging.getLogger(__name__)

class AIAssistant:
    def __init__(self):
        self.conversation_history = []
        
    async def call_ollama(self, prompt, model=DEFAULT_MODEL):
        """Chiama l'API di Ollama per generare una risposta"""
        try:
            # Aggiungi il contesto dei tool disponibili
            system_prompt = """Sei un assistente AI che può aiutare con la ricerca di giochi. 
            Hai accesso a questi tool:
            1. add_game_to_list(game_name) - Aggiunge un gioco alla lista per la ricerca automatica
            2. start_monitoring() - Avvia il monitoraggio automatico dei download
            
            Quando l'utente chiede un gioco, usa add_game_to_list() e poi start_monitoring().
            Rispondi sempre in italiano e sii amichevole."""
            
            full_prompt = f"{system_prompt}\n\nUtente: {prompt}\nAssistente:"
            
            payload = {
                "model": model,
                "prompt": full_prompt,
                "stream": False,
                "options": {
                    "temperature": 0.7,
                    "top_p": 0.9
                }
            }
            
            response = requests.post(OLLAMA_URL, json=payload, timeout=30)
            if response.status_code == 200:
                result = response.json()
                return result.get("response", "Scusa, non riesco a rispondere al momento.")
            else:
                return f"Errore nella comunicazione con l'AI: {response.status_code}"
                
        except Exception as e:
            logger.error(f"Errore Ollama: {e}")
            return "Scusa, c'è stato un problema con l'AI. Riprova tra poco."
    
    def add_game_to_list(self, game_name):
        """Tool: Aggiunge un gioco alla lista"""
        try:
            with open(LISTA_GIOCHI_FILE, "a", encoding="utf-8") as f:
                f.write(f"{game_name}\n")
            logger.info(f"Gioco aggiunto alla lista: {game_name}")
            return f"✅ Ho aggiunto '{game_name}' alla lista dei giochi da cercare!"
        except Exception as e:
            logger.error(f"Errore aggiunta gioco: {e}")
            return f"❌ Errore nell'aggiungere il gioco: {str(e)}"
    
    async def start_monitoring(self):
        """Tool: Avvia il processo di automazione"""
        try:
            # Avvia Automazione Giochi (che ora avvia automaticamente TelegramBot se trova link)
            logger.info("Avvio Automazione Giochi...")
            result = subprocess.run([sys.executable, AUTOMAZIONE_SCRIPT],
                                  capture_output=True, text=True, timeout=300)

            if result.returncode == 0:
                return "🚀 Ho avviato la ricerca automatica! Se trovo qualcosa, il sistema di monitoraggio si attiverà automaticamente."
            else:
                return f"⚠️ Problema durante la ricerca: {result.stderr}"

        except subprocess.TimeoutExpired:
            return "⏱️ La ricerca sta richiedendo più tempo del previsto, continua in background..."
        except Exception as e:
            logger.error(f"Errore avvio automazione: {e}")
            return f"❌ Errore nell'avviare l'automazione: {str(e)}"
    
    async def process_message(self, message):
        """Processa il messaggio dell'utente e decide se usare i tool"""
        # Controlla se il messaggio riguarda la ricerca di giochi
        game_keywords = ["voglio", "cerco", "scarica", "gioco", "game", "download"]
        message_lower = message.lower()
        
        if any(keyword in message_lower for keyword in game_keywords):
            # Estrai il nome del gioco (logica semplificata)
            # In un'implementazione più avanzata, useresti NLP
            words = message.split()
            
            # Cerca pattern come "voglio [nome gioco]" o "cerco [nome gioco]"
            game_name = None
            for i, word in enumerate(words):
                if word.lower() in ["voglio", "cerco", "scarica"] and i + 1 < len(words):
                    game_name = " ".join(words[i+1:])
                    break
            
            if game_name:
                # Usa i tool
                add_result = self.add_game_to_list(game_name)
                monitor_result = await self.start_monitoring()
                
                return f"{add_result}\n{monitor_result}"
        
        # Per altri messaggi, usa solo l'AI
        return await self.call_ollama(message)

# Istanza globale dell'assistente
assistant = AIAssistant()

async def start_command(update: Update, context: CallbackContext):
    """Comando /start"""
    if update.effective_user.id != OWNER_ID:
        await update.message.reply_text("❌ Non sei autorizzato a usare questo bot.")
        return
    
    welcome_msg = """🤖 Ciao! Sono il tuo assistente AI personale!

Posso aiutarti con:
• 🎮 Ricerca automatica di giochi
• 💬 Conversazioni generali
• 🔧 Automazione di download

Dimmi cosa vuoi fare! Ad esempio:
"Voglio Cyberpunk 2077" o "Cerco The Witcher 3"
"""
    await update.message.reply_text(welcome_msg)

async def handle_message(update: Update, context: CallbackContext):
    """Gestisce tutti i messaggi"""
    if update.effective_user.id != OWNER_ID:
        return
    
    user_message = update.message.text
    logger.info(f"Messaggio ricevuto: {user_message}")
    
    # Mostra che sta scrivendo
    await context.bot.send_chat_action(chat_id=update.effective_chat.id, action="typing")
    
    try:
        # Processa il messaggio con l'assistente
        response = await assistant.process_message(user_message)
        await update.message.reply_text(response)
        
    except Exception as e:
        logger.error(f"Errore nella gestione del messaggio: {e}")
        await update.message.reply_text("❌ Scusa, c'è stato un errore. Riprova tra poco.")

async def models_command(update: Update, context: CallbackContext):
    """Comando per vedere i modelli disponibili"""
    if update.effective_user.id != OWNER_ID:
        return
    
    try:
        response = requests.get("http://localhost:11434/api/tags")
        if response.status_code == 200:
            models = response.json().get("models", [])
            model_list = "\n".join([f"• {model['name']}" for model in models])
            await update.message.reply_text(f"🤖 Modelli disponibili:\n{model_list}")
        else:
            await update.message.reply_text("❌ Errore nel recuperare i modelli Ollama")
    except Exception as e:
        await update.message.reply_text(f"❌ Errore: {str(e)}")

def main():
    """Funzione principale"""
    logger.info("🚀 Avvio AI Assistant Bot...")
    
    # Crea l'applicazione
    app = Application.builder().token(TOKEN).build()
    
    # Aggiungi i gestori
    app.add_handler(CommandHandler("start", start_command))
    app.add_handler(CommandHandler("models", models_command))
    app.add_handler(MessageHandler(filters.TEXT & ~filters.COMMAND, handle_message))
    
    # Avvia il bot
    logger.info("✅ Bot avviato! In ascolto...")
    app.run_polling()

if __name__ == "__main__":
    main()
