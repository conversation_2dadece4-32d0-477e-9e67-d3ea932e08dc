#!/usr/bin/env python3
"""
Script per avviare l'intero sistema AI Assistant
Gest<PERSON>ce l'avvio coordinato di tutti i componenti
"""

import subprocess
import sys
import time
import os
import requests
import psutil
from pathlib import Path

# Configurazioni
OLLAMA_URL = "http://localhost:11434/api/tags"
AI_BOT_SCRIPT = "AI_Assistant_Bot.py"
TELEGRAM_BOT_SCRIPT = r"Telegram Bot\TelegramBot.py"

def check_ollama():
    """Verifica se Ollama è in esecuzione"""
    print("🔍 Controllo Ollama...")
    try:
        response = requests.get(OLLAMA_URL, timeout=5)
        if response.status_code == 200:
            models = response.json().get("models", [])
            print(f"✅ Ollama attivo con {len(models)} modelli")
            for model in models:
                print(f"   • {model['name']}")
            return True
        else:
            print("❌ Ollama non risponde correttamente")
            return False
    except Exception as e:
        print(f"❌ Ollama non raggiungibile: {e}")
        return False

def start_ollama():
    """Avvia Ollama se non è già in esecuzione"""
    print("🚀 Avvio Ollama...")
    try:
        # Controlla se Ollama è già in esecuzione
        for proc in psutil.process_iter(['pid', 'name']):
            if 'ollama' in proc.info['name'].lower():
                print("✅ Ollama già in esecuzione")
                return True
        
        # Avvia Ollama
        subprocess.Popen(["ollama", "serve"], 
                        stdout=subprocess.DEVNULL, 
                        stderr=subprocess.DEVNULL)
        
        # Aspetta che si avvii
        for i in range(30):  # Max 30 secondi
            time.sleep(1)
            if check_ollama():
                return True
            print(f"⏳ Attesa Ollama... ({i+1}/30)")
        
        print("❌ Timeout nell'avvio di Ollama")
        return False
        
    except Exception as e:
        print(f"❌ Errore nell'avviare Ollama: {e}")
        return False

def check_files():
    """Verifica che tutti i file necessari esistano"""
    print("📁 Controllo file necessari...")
    
    required_files = [
        AI_BOT_SCRIPT,
        TELEGRAM_BOT_SCRIPT,
        "Automazione Giochi.py"
    ]
    
    missing_files = []
    for file in required_files:
        if not os.path.exists(file):
            missing_files.append(file)
        else:
            print(f"✅ {file}")
    
    if missing_files:
        print("❌ File mancanti:")
        for file in missing_files:
            print(f"   • {file}")
        return False
    
    return True

def create_required_directories():
    """Crea le directory necessarie"""
    print("📂 Creazione directory necessarie...")
    
    directories = [
        r"c:\Users\<USER>\Desktop",
        "processed"
    ]
    
    for directory in directories:
        os.makedirs(directory, exist_ok=True)
        print(f"✅ {directory}")

def create_required_files():
    """Crea i file necessari se non esistono"""
    print("📄 Creazione file necessari...")

    # Path corretti per la cartella liste
    base_dir = os.path.dirname(os.path.abspath(__file__))
    liste_dir = os.path.join(base_dir, "liste")

    # Crea la cartella liste se non esiste
    os.makedirs(liste_dir, exist_ok=True)

    files = {
        os.path.join(liste_dir, "lista_giochi.txt"): "",
        os.path.join(liste_dir, "lista link download.txt"): "",
        os.path.join(liste_dir, "cartelle_giochi.txt"): ""
    }

    for file_path, default_content in files.items():
        if not os.path.exists(file_path):
            with open(file_path, "w", encoding="utf-8") as f:
                f.write(default_content)
            print(f"✅ Creato {file_path}")
        else:
            print(f"✅ {file_path} già esistente")

def start_ai_bot():
    """Avvia il bot AI principale"""
    print("🤖 Avvio AI Assistant Bot...")
    try:
        process = subprocess.Popen([sys.executable, AI_BOT_SCRIPT])
        print(f"✅ AI Bot avviato (PID: {process.pid})")
        return process
    except Exception as e:
        print(f"❌ Errore nell'avviare AI Bot: {e}")
        return None

def main():
    """Funzione principale"""
    print("=" * 50)
    print("🚀 AVVIO SISTEMA AI ASSISTANT")
    print("=" * 50)
    
    # 1. Controllo file
    if not check_files():
        print("\n❌ Alcuni file necessari sono mancanti!")
        input("Premi Enter per uscire...")
        return
    
    # 2. Crea directory e file necessari
    create_required_directories()
    create_required_files()
    
    # 3. Controlla/Avvia Ollama
    if not check_ollama():
        print("\n🔧 Ollama non è in esecuzione, provo ad avviarlo...")
        if not start_ollama():
            print("\n❌ Impossibile avviare Ollama!")
            print("💡 Assicurati che Ollama sia installato e configurato correttamente")
            input("Premi Enter per uscire...")
            return
    
    # 4. Avvia il bot AI
    ai_bot_process = start_ai_bot()
    if not ai_bot_process:
        print("\n❌ Impossibile avviare il bot AI!")
        input("Premi Enter per uscire...")
        return
    
    print("\n" + "=" * 50)
    print("✅ SISTEMA AVVIATO CON SUCCESSO!")
    print("=" * 50)
    print("🤖 AI Assistant Bot è attivo")
    print("💬 Puoi iniziare a chattare su Telegram")
    print("🎮 Prova a scrivere: 'Voglio [nome gioco]'")
    print("\n🛑 Premi Ctrl+C per fermare il sistema")
    print("=" * 50)
    
    try:
        # Mantieni il processo principale attivo
        ai_bot_process.wait()
    except KeyboardInterrupt:
        print("\n🛑 Arresto del sistema...")
        try:
            ai_bot_process.terminate()
            ai_bot_process.wait(timeout=5)
        except:
            ai_bot_process.kill()
        print("✅ Sistema arrestato")

if __name__ == "__main__":
    main()
